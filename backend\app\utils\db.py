"""
Database utility functions.
"""
from typing import Any, TypeVar, Optional
from sqlalchemy.sql import functions
from sqlalchemy import Column

T = TypeVar('T')

def get_value(column: Column[T], default: Optional[T] = None) -> T:
    """
    Safely get the value from a SQLAlchemy column.
    
    Args:
        column: The SQLAlchemy column
        default: Default value if column is None
        
    Returns:
        The actual value of the column
    """
    try:
        if hasattr(column, 'scalar'):
            return column.scalar()
        elif hasattr(column, '_value'):
            return column._value
        return default if column is None else column
    except Exception:
        return default