#!/usr/bin/env python3
"""
Simple test server to verify FastAPI setup.
"""

from fastapi import FastAPI

app = FastAPI(title="Diorite Test Server")

@app.get("/")
async def root():
    return {"message": "Diorite test server is running!"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
