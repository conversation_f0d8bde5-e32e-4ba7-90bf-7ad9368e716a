#!/usr/bin/env python3
"""
Test script to verify configuration loading.
"""

try:
    print("Testing configuration loading...")
    from app.core.config import settings
    print("✅ Configuration loaded successfully!")
    print(f"Project: {settings.PROJECT_NAME}")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Database URL: {settings.DATABASE_URL}")
    print(f"CORS Origins: {settings.ALLOWED_ORIGINS}")
    print(f"File Types: {settings.ALLOWED_FILE_TYPES}")
    
except Exception as e:
    print(f"❌ Configuration loading failed: {e}")
    import traceback
    traceback.print_exc()
