"""
Celery tasks for AI agent operations.
"""
from typing import Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.chat import ChatSession, ChatMessage
from app.services.simple_ai_agent import simple_data_science_agent
import structlog

logger = structlog.get_logger()


@celery_app.task(bind=True)
def process_agent_message_task(self: Any, session_id: int, message_content: str, user_id: int):
    """Process AI agent message asynchronously."""
    db: Session = SessionLocal()

    try:
        self.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Processing message"}
        )

        # Get chat session
        session = db.query(ChatSession).filter(
            ChatSession.id == session_id,
            ChatSession.user_id == user_id
        ).first()

        if not session:
            raise Exception(f"Chat session {session_id} not found")

        logger.info("Processing agent message", session_id=session_id, user_id=user_id)

        # Process message with AI agent
        self.update_state(
            state="PROGRESS",
            meta={"current": 50, "total": 100, "status": "AI agent processing"}
        )

        context = session.context_data or {}
        # Ensure context is a dict (handle SQLAlchemy type issues)
        if not isinstance(context, dict):
            context = {}

        agent_response = simple_data_science_agent.process_message(
            message_content,
            user_id,
            context
        )

        # Save agent response
        self.update_state(
            state="PROGRESS",
            meta={"current": 80, "total": 100, "status": "Saving response"}
        )

        agent_message = ChatMessage(
            session_id=session_id,
            content=agent_response["response"],
            message_type="assistant",
            metadata={
                "success": agent_response["success"],
                "intermediate_steps": agent_response.get("intermediate_steps", []),
                "processed_async": True
            }
        )

        db.add(agent_message)

        # Update session
        session.updated_at = func.now()  # type: ignore
        db.commit()
        db.refresh(agent_message)

        logger.info("Agent message processed",
                   session_id=session_id,
                   message_id=agent_message.id,
                   success=agent_response["success"])

        return {
            "status": "completed",
            "session_id": session_id,
            "message_id": agent_message.id,
            "success": agent_response["success"],
            "response": agent_response["response"]
        }

    except Exception as e:
        logger.error("Agent message processing failed",
                    session_id=session_id,
                    user_id=user_id,
                    error=str(e))

        # Save error message
        try:
            error_message = ChatMessage(
                session_id=session_id,
                content=f"I encountered an error while processing your message: {str(e)}",
                message_type="assistant",
                metadata={"success": False, "error": str(e), "processed_async": True}
            )
            db.add(error_message)
            db.commit()
        except:
            pass  # Don't fail if we can't save error message

        raise

    finally:
        db.close()


@celery_app.task(bind=True)
def generate_insights_task(self: Any, dataset_id: int, user_id: int):
    """Generate automated insights for a dataset."""
    db: Session = SessionLocal()

    try:
        self.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Starting insight generation"}
        )

        from app.models.dataset import Dataset

        # Get dataset
        dataset = db.query(Dataset).filter(
            Dataset.id == dataset_id,
            Dataset.owner_id == user_id
        ).first()

        if not dataset:
            raise Exception(f"Dataset {dataset_id} not found")

        logger.info("Generating insights", dataset_id=dataset_id, user_id=user_id)

        # Load dataset
        self.update_state(
            state="PROGRESS",
            meta={"current": 20, "total": 100, "status": "Loading dataset"}
        )

        from app.services.data_processor import data_processor
        file_path: str = dataset.file_path  # type: ignore
        df = data_processor.load_dataset(file_path)

        # Generate insights using AI agent
        self.update_state(
            state="PROGRESS",
            meta={"current": 50, "total": 100, "status": "Analyzing data"}
        )

        insights_prompt = f"""
        Please analyze this dataset and provide key insights:

        Dataset: {dataset.name}
        Shape: {df.shape}
        Columns: {list(df.columns)}

        Provide insights about:
        1. Data quality and completeness
        2. Key patterns and trends
        3. Potential data issues
        4. Recommendations for analysis
        """

        context = {
            "current_dataset_id": dataset_id,
            "dataset_info": {
                "name": dataset.name,
                "shape": df.shape,
                "columns": list(df.columns)
            }
        }

        agent_response = simple_data_science_agent.process_message(
            insights_prompt,
            user_id,
            context
        )

        # Save insights
        self.update_state(
            state="PROGRESS",
            meta={"current": 90, "total": 100, "status": "Saving insights"}
        )

        # You could save insights to a separate table or as dataset metadata
        # For now, we'll return them

        logger.info("Insights generated", dataset_id=dataset_id, user_id=user_id)

        return {
            "status": "completed",
            "dataset_id": dataset_id,
            "insights": agent_response["response"],
            "success": agent_response["success"]
        }

    except Exception as e:
        logger.error("Insight generation failed",
                    dataset_id=dataset_id,
                    user_id=user_id,
                    error=str(e))
        raise

    finally:
        db.close()
