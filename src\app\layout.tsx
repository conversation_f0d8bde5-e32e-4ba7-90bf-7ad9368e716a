import type { Metadata } from "next";
// Temporarily disable Google Fonts to avoid loading issues
// import { Inter } from "next/font/google";
import "./globals.css";

// Use system fonts for now
// const inter = Inter({
//   subsets: ["latin"],
//   variable: "--font-inter",
// });

export const metadata: Metadata = {
  title: "Diorite - AI Data Science Agent",
  description: "AI-powered data science agent for intelligent data analysis and insights",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className="font-sans antialiased bg-background text-text-dark"
      >
        {children}
      </body>
    </html>
  );
}
