"""
Chat models for AI agent conversations and session management.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime, Text, ForeignKey, JSON, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class ChatSession(Base):
    """Chat session model for managing AI agent conversations."""
    
    __tablename__ = "chat_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=True)  # User-defined or auto-generated title
    
    # Session context
    context_data = Column(JSON, nullable=True)  # Current dataset, models, etc.
    session_state = Column(JSON, nullable=True)  # Agent state, memory, etc.
    
    # Session status
    is_active = Column(Boolean, default=True)
    
    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<ChatSession(id={self.id}, user_id={self.user_id}, title='{self.title}')>"


class ChatMessage(Base):
    """Individual chat messages within a session."""
    
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    
    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), nullable=False)  # user, assistant, system, tool
    
    # Message metadata
    metadata = Column(JSON, nullable=True)  # Tool calls, function results, etc.
    
    # Tool execution results
    tool_calls = Column(JSON, nullable=True)  # If message contains tool calls
    tool_results = Column(JSON, nullable=True)  # Results from tool execution
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")
    
    def __repr__(self):
        return f"<ChatMessage(id={self.id}, session_id={self.session_id}, type='{self.message_type}')>"


class AgentTool(Base):
    """Available tools for the AI agent."""
    
    __tablename__ = "agent_tools"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=False)
    
    # Tool configuration
    function_name = Column(String(100), nullable=False)  # Python function to call
    parameters_schema = Column(JSON, nullable=False)  # JSON schema for parameters
    
    # Tool metadata
    category = Column(String(50), nullable=False)  # data_analysis, visualization, ml, etc.
    is_active = Column(Boolean, default=True)
    requires_dataset = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<AgentTool(id={self.id}, name='{self.name}', category='{self.category}')>"
